{"c": ["app/layout", "app/dashboard/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CCode%5C%5CPortfolio%5C%5CNewMRH%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./src/app/dashboard/page.tsx", "(app-pages-browser)/./src/components/dashboard/DashboardLayout.tsx", "(app-pages-browser)/./src/components/dashboard/DashboardSidebar.tsx", "(app-pages-browser)/./src/components/dashboard/QuickActions.tsx", "(app-pages-browser)/./src/components/dashboard/RecentActivity.tsx"]}