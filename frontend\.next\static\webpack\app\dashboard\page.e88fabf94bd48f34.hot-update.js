"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/DashboardSidebar.tsx":
/*!*******************************************************!*\
  !*** ./src/components/dashboard/DashboardSidebar.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardSidebar: () => (/* binding */ DashboardSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ DashboardSidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst DashboardSidebar = (param)=>{\n    let { isOpen, onToggle, className = '' } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const sidebarItems = [\n        {\n            id: 'overview',\n            label: 'Overview',\n            href: '/dashboard',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardSidebar.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardSidebar.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardSidebar.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, undefined)\n        },\n        {\n            id: 'properties',\n            label: 'Properties',\n            href: '/dashboard/properties',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardSidebar.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardSidebar.tsx\",\n                lineNumber: 52,\n                columnNumber: 9\n            }, undefined),\n            badge: '12'\n        },\n        {\n            id: 'analytics',\n            label: 'Analytics',\n            href: '/dashboard/analytics',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardSidebar.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardSidebar.tsx\",\n                lineNumber: 68,\n                columnNumber: 9\n            }, undefined)\n        },\n        {\n            id: 'messages',\n            label: 'Messages',\n            href: '/dashboard/messages',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardSidebar.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardSidebar.tsx\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, undefined),\n            badge: '3'\n        },\n        {\n            id: 'profile',\n            label: 'Profile',\n            href: '/dashboard/profile',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardSidebar.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardSidebar.tsx\",\n                lineNumber: 99,\n                columnNumber: 9\n            }, undefined)\n        },\n        {\n            id: 'settings',\n            label: 'Settings',\n            href: '/dashboard/settings',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardSidebar.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardSidebar.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardSidebar.tsx\",\n                lineNumber: 114,\n                columnNumber: 9\n            }, undefined)\n        }\n    ];\n    const isActiveItem = (href)=>{\n        if (href === '/dashboard') {\n            return pathname === '/dashboard';\n        }\n        return pathname.startsWith(href);\n    };\n    // Get user initials for avatar\n    const getUserInitials = ()=>{\n        if (user === null || user === void 0 ? void 0 : user.name) {\n            return user.name.split(' ').map((n)=>n.charAt(0)).join('').toUpperCase().slice(0, 2);\n        }\n        return (user === null || user === void 0 ? void 0 : user.email.charAt(0).toUpperCase()) || 'U';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\",\n                onClick: onToggle,\n                \"aria-hidden\": \"true\"\n            }, void 0, false, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardSidebar.tsx\",\n                lineNumber: 151,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                id: \"dashboard-sidebar\",\n                className: \"\\n        fixed top-0 left-0 h-full bg-white border-r border-slate-200 z-50\\n        transform transition-transform duration-300 ease-in-out\\n        \".concat(isOpen ? 'translate-x-0' : '-translate-x-full', \"\\n        lg:translate-x-0 lg:relative lg:z-auto lg:h-screen\\n        w-64 flex flex-col shadow-lg lg:shadow-none flex-shrink-0\\n        \").concat(className, \"\\n      \"),\n                \"aria-label\": \"Dashboard navigation\",\n                role: \"navigation\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sidebar px-4 py-2 lg:px-6 lg:py-5 border-b border-slate-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-gradient-to-r from-blue-900 to-slate-800 rounded-full flex items-center justify-center text-white font-semibold\",\n                                    children: getUserInitials()\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardSidebar.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-semibold text-slate-900 truncate\",\n                                            children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardSidebar.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-slate-500 truncate\",\n                                            children: user === null || user === void 0 ? void 0 : user.email\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardSidebar.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardSidebar.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardSidebar.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardSidebar.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex-1 p-4 space-y-1\",\n                        children: sidebarItems.map((item)=>{\n                            const isActive = isActiveItem(item.href);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: item.href,\n                                className: \"\\n                  flex items-center justify-between px-3 py-2 rounded-lg text-sm font-medium\\n                  transition-all duration-200 group focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\\n                  \".concat(isActive ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' : 'text-slate-600 hover:bg-slate-50 hover:text-slate-900', \"\\n                \"),\n                                onClick: ()=>{\n                                    // Close mobile sidebar when item is clicked\n                                    if (window.innerWidth < 1024) {\n                                        onToggle();\n                                    }\n                                },\n                                \"aria-current\": isActive ? 'page' : undefined,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"\".concat(isActive ? 'text-blue-700' : 'text-slate-400 group-hover:text-slate-600'),\n                                                children: item.icon\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardSidebar.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: item.label\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardSidebar.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardSidebar.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full\",\n                                        children: item.badge\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardSidebar.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, item.id, true, {\n                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardSidebar.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 15\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardSidebar.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-t border-slate-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-slate-500 text-center\",\n                            children: \"MyRealHub Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardSidebar.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardSidebar.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardSidebar.tsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(DashboardSidebar, \"ksVRUGqrhVGhRjk6vGyPsFYVFrs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = DashboardSidebar;\nvar _c;\n$RefreshReg$(_c, \"DashboardSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/DashboardSidebar.tsx\n"));

/***/ })

});